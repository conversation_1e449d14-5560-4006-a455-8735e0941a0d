from fastapi import HTTPException
from fastapi import APIRouter, Request, Depends, status, UploadFile, Response
from .middlewares import with_authentication
from models.user import Role, UserResume, JobSeekerSkill
from models.job import Job, Bookmark, JobApplication, ExternalJob
from models.user import User
from db.connection import session
from pydantic import BaseModel, EmailStr
from .helpers import sendError, sendSuccess
from typing import List, Optional
import datetime
from sqlalchemy.orm import joinedload
from sqlalchemy import func

router = APIRouter(
    prefix="/job",
    tags=["job"],
    dependencies=[Depends(with_authentication([Role.EMPLOYER, Role.ADMIN]))],
)

app_router = APIRouter(
    prefix="/job",
    tags=["job"],
    dependencies=[
        Depends(
            with_authentication(
                [Role.CREATOR, Role.ADMIN, Role.JOB_SEEKER,
                    Role.EMPLOYER, Role.ANALYST]
            )
        )
    ],
)


class JobData(BaseModel):
    id: Optional[int] = None
    title: Optional[str] = None
    description: str = None
    requirements: Optional[List[str]] = None
    skills: Optional[List[str]] = None
    position: Optional[str] = None
    image: Optional[str] = None
    location: Optional[str] = None
    position: Optional[str] = None
    expiry: Optional[datetime.date] = None
    salary: Optional[float] = None
    type: Optional[str] = None
    company: Optional[str] = None


class JobApplicationData(BaseModel):
    status: str
    user_id: int
    job_id: int


@router.post("/")
async def post_job(request: Request, data: JobData):
    user_id = request.state.user["id"]
    try:
        job = Job()
        job.title = data.title
        job.requirements = data.requirements
        job.skills = data.skills
        job.description = data.description or data.title
        job.image = data.image
        job.user_id = user_id
        job.location = data.location
        job.position = data.position
        job.expiry = data.expiry
        job.salary = data.salary
        job.type = data.type
        job.company = data.company
        session.add(job)
        session.commit()
        return sendSuccess("created")
    except Exception as err:
        session.rollback()
        print(err)
        return sendError(err.args)
    finally:
        session.close()


@router.put("/")
async def update_job(request: Request, data: JobData):
    # user_id = request.state.user["id"]
    try:
        job = session.query(Job).filter(Job.id == data.id).first()
        if job is None:
            return sendError("Job not found")

        # if job.user_id != user_id:
        #     return sendError("You do not have permission to update this job")

        # Update the specific fields provided in the request
        if data.title is not None:
            job.title = data.title
        if data.requirements is not None:
            job.requirements = data.requirements
        if data.skills is not None:
            job.skills = data.skills
        if data.description is not None:
            job.description = data.description
        if data.image is not None:
            job.image = data.image
        if data.location is not None:
            job.location = data.location
        if data.position is not None:
            job.position = data.position
        if data.expiry is not None:
            job.expiry = data.expiry
        if data.salary is not None:
            job.salary = data.salary
        if data.type is not None:
            job.type = data.type
        if data.company is not None:
            job.company = data.company

        session.commit()
        return sendSuccess("Job updated successfully")
    except Exception as err:
        session.rollback()
        print(err)
        return sendError(err.args)
    finally:
        session.close()


@app_router.get("/")
async def get_jobs():
    try:
        jobs = session.query(Job).all()
        job_list = []
        for job in jobs:
            job_dict = {
                "id": job.id,
                "title": job.title,
                "location": job.location,
                "expiry": job.expiry,
                "salary": job.salary,
                "description": job.description,
                "requirements": job.requirements,
                "image": job.image,
                "type": job.type,
                "position": job.position,
                "skills": job.skills,
                "company": job.company,
                "user_id": job.user_id,
            }
            job_list.append(job_dict)
        return sendSuccess(job_list)
    except Exception as err:
        session.rollback()
        print(err)
        return sendError(err.args)
    finally:
        session.close()


@router.delete("/{job_id}")
async def delete_job(job_id: int, request: Request):
    # user_id = request.state.user["id"]
    try:
        job = session.query(Job).filter(Job.id == job_id).first()
        if job is None:
            return sendError("Job not found")

        session.delete(job)
        session.commit()
        return sendSuccess("Job deleted successfully")
    except Exception as err:
        session.rollback()
        print(err)
        return sendError(err.args)
    finally:
        session.close()


@app_router.post("/bookmark/{item_id}")
async def create_bookmark(item_id: str, request: Request):
    user_id = request.state.user["id"]

    try:
        # Try to parse item_id as int to check if it's an internal job
        try:
            internal_id = int(item_id)
            job = session.query(Job).filter(Job.id == internal_id).first()
            if job:
                # Check if already bookmarked as internal
                existing = session.query(Bookmark).filter(
                    Bookmark.job_id == internal_id,
                    Bookmark.user_id == user_id,
                ).first()

                if existing:
                    return sendSuccess("already bookmarked")

                # Create internal bookmark
                bk = Bookmark(
                    user_id=user_id,
                    job_id=internal_id,
                    created=datetime.datetime.now()
                )
                session.add(bk)
                session.commit()
                return sendSuccess("created")
        except ValueError:
            # Not an integer => external job
            pass

        # If we reach here, it's an external job
        existing = session.query(Bookmark).filter(
            Bookmark.external_job_id == item_id,
            Bookmark.user_id == user_id
        ).first()

        if existing:
            return sendSuccess("already bookmarked")

        # Create external bookmark
        bk = Bookmark(
            user_id=user_id,
            external_job_id=item_id,
            created=datetime.datetime.now()
        )
        session.add(bk)
        session.commit()
        return sendSuccess("created")

    except Exception as err:
        session.rollback()
        print(f"Error: {err}")
        return sendError("Something went wrong.")
    finally:
        session.close()


@app_router.get("/bookmarks")
async def get_user_bookmarks(request: Request):
    user_id = request.state.user["id"]

    try:
        bookmarks = (
            session.query(Bookmark)
            .filter(Bookmark.user_id == user_id)
            .all()
        )

        if not bookmarks:
            return sendSuccess([])

        bookmark_list = []
        for bookmark in bookmarks:
            if bookmark.job_id:  # internal job
                job = session.query(Job).filter(
                    Job.id == bookmark.job_id).first()
                if job:
                    bk_dict = {
                        "id": job.id,
                        "title": job.title,
                        "location": job.location,
                        "expiry": job.expiry,
                        "salary": job.salary,
                        "description": job.description,
                        "requirements": job.requirements,
                        "image": job.image,
                        "type": job.type,
                        "position": job.position,
                        "skills": job.skills,
                        "company": job.company,
                        "is_external": False,
                    }
                    bookmark_list.append(bk_dict)
            elif bookmark.external_job_id:  # external job
                external_job = session.query(ExternalJob).filter(
                    ExternalJob.id == bookmark.external_job_id).first()
                if external_job:
                    bk_dict = {
                        "id": external_job.id,
                        "title": external_job.title,
                        "location": external_job.location,
                        "expiry": None,
                        "salary": None,
                        "description": external_job.description,
                        "requirements": None,
                        "image": None,
                        "type": external_job.job_type,
                        "position": None,
                        "skills": external_job.skills,
                        "company": external_job.company,
                        "is_external": True,
                    }
                    bookmark_list.append(bk_dict)

        return sendSuccess(bookmark_list)
    except Exception as err:
        session.rollback()
        print(err)
        return sendError(str(err))
    finally:
        session.close()


@app_router.delete("/bookmarks/{item_id}")
async def delete_bookmark(item_id: str, request: Request):
    user_id = request.state.user["id"]

    try:
        try:
            item_id_int = int(item_id)
        except ValueError:
            item_id_int = None

        bookmark = session.query(Bookmark).filter(
            Bookmark.user_id == user_id
        ).filter(
            (Bookmark.job_id == item_id_int) | (
                Bookmark.external_job_id == item_id)
        ).first()

        if bookmark is None:
            # <-- Return properly here
            return sendSuccess("Bookmark not found")

        session.delete(bookmark)
        session.commit()
        return sendSuccess("Bookmark removed")

    except Exception as err:
        session.rollback()
        print(f"Error: {err}")
        return sendError("Something went wrong.")
    finally:
        session.close()


@app_router.post("/application/{job_id}")
async def apply_for_job(job_id: int, request: Request):
    user_id = request.state.user["id"]
    try:
        job = (
            session.query(JobApplication)
            .filter(JobApplication.job_id == job_id)
            .first()
        )

        if job:
            return sendSuccess("already applied")

        ap = JobApplication()
        ap.user_id = user_id
        ap.job_id = job_id
        ap.status = "pending"
        session.add(ap)
        session.commit()
        return sendSuccess("created")
    except Exception as err:
        session.rollback()
        print(err)
        return sendError(err.args)
    finally:
        session.close()


@app_router.get("/applications")
async def get_user_applications(request: Request):
    user_id = request.state.user["id"]
    try:
        applications = (
            session.query(Job, JobApplication.status)
            .join(JobApplication)
            .filter(JobApplication.user_id == user_id)
            .all()
        )
        application_list = []
        for job, status in applications:
            ap_dict = {
                "id": job.id,
                "title": job.title,
                "location": job.location,
                "expiry": job.expiry,
                "salary": job.salary,
                "description": job.description,
                "requirements": job.requirements,
                "image": job.image,
                "type": job.type,
                "position": job.position,
                "skills": job.skills,
                "company": job.company,
                "status": status,
            }
            application_list.append(ap_dict)
        return sendSuccess(application_list)
    except Exception as err:
        session.rollback()
        print(err)
        return sendError(err.args)
    finally:
        session.close()


@app_router.delete("/application/{job_id}")
async def delete_application(job_id: int, request: Request):
    user_id = request.state.user["id"]
    try:
        application = (
            session.query(JobApplication)
            .filter(JobApplication.user_id == user_id, JobApplication.job_id == job_id)
            .first()
        )

        if application is None:
            return sendError("not found")

        session.delete(application)
        session.commit()
        return sendSuccess("application removed")
    except Exception as err:
        session.rollback()
        print(err)
        return sendError(err.args)
    finally:
        session.close()


@router.get("/applicants")
async def get_applicants(request: Request):
    try:
        applicants = (
            session.query(User, Job, JobApplication.status, JobSeekerSkill)
            .join(JobApplication, User.id == JobApplication.user_id)
            .join(Job, Job.id == JobApplication.job_id)
            .join(JobSeekerSkill, JobSeekerSkill.user_id == JobApplication.user_id)
            .all()
        )
        applicant_list = []

        for applicant, job, status, skills in applicants:
            user_id = applicant.id
            applicant_data = {
                "id": user_id,
                "languages": applicant.profile.languages,
                "name": applicant.name,
                "email": applicant.email,
                "experiences": applicant.experiences,
                "education": applicant.education,
                "img": applicant.profile_image,
            }

            application = {
                "job_title": job.title,
                "job_id": job.id,
                "status": status,
                "skills": [skills.skill.name],
                "resume_links": [
                    str(request.base_url) + "user/file/" + resume.filename
                    for resume in session.query(UserResume).filter(
                        UserResume.user_id == user_id
                    ).all()
                ],
            }

            applicant_data.update(application)
            applicant_list.append(applicant_data)

        return sendSuccess(applicant_list)
    except Exception as err:
        session.rollback()
        print(err)
        return sendError(err.args)
    finally:
        session.close()


@router.put("/status")
async def update_application_status(data: JobApplicationData):
    try:
        app = (
            session.query(JobApplication)
            .filter(
                JobApplication.user_id == data.user_id,
                JobApplication.job_id == data.job_id,
            )
            .first()
        )
        app.status = data.status
        session.commit()
        return sendSuccess("Status updated")
    except Exception as err:
        session.rollback()
        print(err)
        return sendError(err.args)
    finally:
        session.close()
