#!/usr/bin/env python3
"""
Test matching for user 11 specifically
"""

from services.enhanced_matching_system import GenericLLMProcessor
from services.matching_cache_manager import EnhancedMatchingController
from db.connection import session
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_user_11_matching():
    print('=== TESTING USER 11 MATCHING ===')
    user_id = 11

    # Initialize controller
    llm_processor = GenericLLMProcessor()
    controller = EnhancedMatchingController(session, llm_processor)

    # Test external job matching specifically
    print('Testing external job matching...')
    result = controller.process_user_matching_request(
        user_id, 'external_job', force_refresh=True, limit=50)

    print(f'Result keys: {result.keys()}')
    print(f'Recommendations count: {len(result.get("recommendations", []))}')
    print(f'Error: {result.get("error", "None")}')
    print(f'Cache refreshed: {result.get("cache_refreshed", False)}')

    if result.get('recommendations'):
        print('\nAll Recommendations:')
        for rec in result['recommendations']:
            item_data = rec.get('item_data', {})
            skills = item_data.get('skills', [])
            print(
                f'  - {item_data.get("title", "Unknown")}: {rec["match_score"]}% (skills: {rec["skill_match_count"]}) - Job skills: {skills}')

    # Test job matching too
    print('\nTesting job matching...')
    job_result = controller.process_user_matching_request(
        user_id, 'job', force_refresh=True, limit=50)

    print(f'Job result keys: {job_result.keys()}')
    print(
        f'Job recommendations count: {len(job_result.get("recommendations", []))}')
    print(f'Job error: {job_result.get("error", "None")}')

    session.close()


if __name__ == "__main__":
    test_user_11_matching()
