#!/usr/bin/env python3
"""
Test background matching for user 10 specifically
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from db.connection import session
from routes.user_routes import enhanced_background_matching
from models.job import ExternalJobMatch


async def test_background_matching_user_10():
    print('=== TESTING BACKGROUND MATCHING FOR USER 10 ===')
    user_id = 10
    
    # Clear existing matches first
    session.query(ExternalJobMatch).filter(ExternalJobMatch.user_id == user_id).delete()
    session.commit()
    print('Cleared existing matches')
    
    # Run background matching
    print('Running background matching...')
    await enhanced_background_matching(user_id, 40.0, include_courses=True, force_refresh=True)
    
    # Check results
    matches = session.query(ExternalJobMatch).filter(ExternalJobMatch.user_id == user_id).all()
    print(f'Found {len(matches)} matches after background matching')
    
    for match in matches:
        print(f'  - Job {match.external_job_id}: {match.match_score}% (skills: {match.skill_match_count})')
    
    session.close()


if __name__ == "__main__":
    asyncio.run(test_background_matching_user_10())
