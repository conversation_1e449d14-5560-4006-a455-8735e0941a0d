#!/usr/bin/env python3
"""
Test matching for user 10 specifically
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from db.connection import session
from models.job import ExternalJob
from services.enhanced_matching_system import JobCourseMatchingService, GenericLLMProcessor
from services.matching_cache_manager import OptimizedMatchingService, MatchingCacheManager


def test_user_10_matching():
    print('=== TESTING USER 10 MATCHING ===')
    user_id = 10
    
    # Initialize services
    llm_processor = GenericLLMProcessor()
    cache_manager = MatchingCacheManager(session)
    optimized_service = OptimizedMatchingService(session, cache_manager, llm_processor)
    service = JobCourseMatchingService(session, llm_processor)
    
    # Get user profile
    user_profile = service.get_user_profile_from_db(user_id)
    print(f'User profile: {user_profile.user_id if user_profile else "Not found"}')
    print(f'User skills: {user_profile.skills[:10] if user_profile else "None"}')
    
    # Get external jobs with skills
    external_jobs = session.query(ExternalJob).filter(ExternalJob.is_enabled == True).all()
    jobs_with_skills = []
    
    print(f'\nExternal jobs analysis:')
    for job in external_jobs:
        has_skills = job.skills and len(job.skills) > 0
        print(f'  - Job {job.id} ({job.title}): {"✅" if has_skills else "❌"} Skills: {job.skills}')
        if has_skills:
            jobs_with_skills.append({
                'id': job.id,
                'title': job.title,
                'company': job.company,
                'location': job.location,
                'description': job.description or '',
                'skills': job.skills,
                'type': 'external_job'
            })
    
    print(f'\nJobs with skills: {len(jobs_with_skills)}')
    
    # Test manual matching
    if user_profile and jobs_with_skills:
        print(f'\nTesting manual matching...')
        matches = optimized_service.batch_match_items(user_profile, jobs_with_skills, 'external_job', use_cache=False)
        print(f'Manual matches found: {len(matches)}')
        
        for match in matches:
            print(f'  - Job {match["item_id"]}: {match["match_score"]}% (skills: {match["skill_match_count"]})')
            print(f'    Matched skills: {match["matched_skills"]}')
    
    session.close()


if __name__ == "__main__":
    test_user_10_matching()
