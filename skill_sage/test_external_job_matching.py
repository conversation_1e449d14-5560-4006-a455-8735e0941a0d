#!/usr/bin/env python3
"""
Test external job matching specifically
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from db.connection import session
from services.matching_cache_manager import EnhancedMatchingController
from services.enhanced_matching_system import GenericLLMProcessor


def test_external_job_matching():
    print('=== TESTING EXTERNAL JOB MATCHING ===')
    user_id = 10
    
    # Initialize controller
    llm_processor = GenericLLMProcessor()
    controller = EnhancedMatchingController(session, llm_processor)
    
    # Test external job matching specifically
    print('Testing external job matching...')
    result = controller.process_user_matching_request(user_id, 'external_job', force_refresh=True, limit=50)
    
    print(f'Result keys: {result.keys()}')
    print(f'Recommendations count: {len(result.get("recommendations", []))}')
    print(f'Error: {result.get("error", "None")}')
    print(f'Cache refreshed: {result.get("cache_refreshed", False)}')
    
    if result.get('recommendations'):
        print('\nRecommendations:')
        for rec in result['recommendations']:
            item_data = rec.get('item_data', {})
            print(f'  - {item_data.get("title", "Unknown")}: {rec["match_score"]}% (skills: {rec["skill_match_count"]})')
    
    session.close()


if __name__ == "__main__":
    test_external_job_matching()
