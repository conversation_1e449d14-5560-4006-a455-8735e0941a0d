{"python_beginner": {"skill": "python", "level": "beginner", "total_results": 5, "videos": [{"name": "Learn Python in Less than 10 Minutes for Beginners (Fast & Easy)", "description": "In this crash course I'll be teaching you the basics of Python in less than 10 minutes. Python is super easy to learn compared to the other languages!\n\nDownload Python: https://www.python.org/\nDownloa...", "thumbnail": "https://i.ytimg.com/vi/fWjsdhR3z3c/hqdefault.jpg", "url": "https://www.youtube.com/watch?v=fWjsdhR3z3c", "duration": "10:30", "view_count": 923443, "like_count": 21548, "published_date": "2021-05-26", "channel_name": "Indently"}, {"name": "Coding in Python   Part 1", "description": "This is my first video in the series Coding in Python. I will walk you through the book written by <PERSON><PERSON><PERSON> and hope to make it easy for kids to follow the steps to learn python.\n\nThis tutorial ...", "thumbnail": "https://i.ytimg.com/vi/uzyiCpsS0qQ/hqdefault.jpg", "url": "https://www.youtube.com/watch?v=uzyiCpsS0qQ", "duration": "8:37", "view_count": 613028, "like_count": 6772, "published_date": "2019-08-18", "channel_name": "NPStation"}, {"name": "Why Should You Learn Python? | Complete python3 tutorials for beginners", "description": "Python tutorial for beginners: In this tutorial we will learn why everyone should learn python. If you are a beginner and just starting out with programming then python is probably the best place to s...", "thumbnail": "https://i.ytimg.com/vi/eykoKxsYtow/hqdefault.jpg", "url": "https://www.youtube.com/watch?v=eykoKxsYtow", "duration": "5:16", "view_count": 538022, "like_count": 9925, "published_date": "2017-05-23", "channel_name": "codebasics"}, {"name": "Do THIS instead of watching endless tutorials - how I’d learn Python FAST…", "description": "🎓 These are two of the best beginner-friendly Python resources I recommend:\n\n🔹 Python Programming Fundamentals (Datacamp) (https://datacamp.pxf.io/QjG9BM)\n🔹 Associate Python Developer Certificate (Dat...", "thumbnail": "https://i.ytimg.com/vi/mB0EBW-vDSQ/hqdefault.jpg", "url": "https://www.youtube.com/watch?v=mB0EBW-vDSQ", "duration": "10:34", "view_count": 256595, "like_count": 11861, "published_date": "2025-05-16", "channel_name": "Tech With <PERSON>"}, {"name": "Python 3 Tutorial for Beginners #1 - Why Learn Python?", "description": "Hey gang, welcome to your very first python 3 tutorial for beginners. In this video I'll be going over why it's a good idea to learn python, as well as introducing some of the tools I'll be using for ...", "thumbnail": "https://i.ytimg.com/vi/Ozrduu2W9B8/hqdefault.jpg", "url": "https://www.youtube.com/watch?v=Ozrduu2W9B8", "duration": "5:21", "view_count": 232925, "like_count": 2417, "published_date": "2017-06-20", "channel_name": "Net Ninja"}]}, "javascript_intermediate": {"skill": "javascript", "level": "intermediate", "total_results": 3, "videos": [{"name": "100+ JavaScript Concepts you Need to Know", "description": "The ultimate 10 minute JavaScript course that quickly breaks down over 100 key concepts every web developer should know. Learn the basics of JS and start building apps on the web, mobile, desktop, and...", "thumbnail": "https://i.ytimg.com/vi/lkIFF4maKMU/hqdefault.jpg", "url": "https://www.youtube.com/watch?v=lkIFF4maKMU", "duration": "12:24", "view_count": 2663148, "like_count": 98936, "published_date": "2022-11-22", "channel_name": "Fireship"}, {"name": "How to Learn JavaScript FAST in 2025", "description": "If you want to get an easy introduction to JavaScript to help you take your code to the next level, check out this free resource from Hubspot! https://clickhubspot.com/ibs4\n\n-----------\n\n🚀 Learn JavaS...", "thumbnail": "https://i.ytimg.com/vi/xB3ZmUH6GqU/hqdefault.jpg", "url": "https://www.youtube.com/watch?v=xB3ZmUH6GqU", "duration": "12:32", "view_count": 119977, "like_count": 6212, "published_date": "2025-04-01", "channel_name": "<PERSON>"}, {"name": "5 Must Know Javascript Advanced Concepts", "description": "🎁 Free PDF: Senior Developer Starter Kit (Checklist + 30-Day Plan) → https://monsterlessons-academy.com/newsletter_subscribers/senior_starter_kit?utm_source=youtube&utm_medium=description_link\n🚀 Go fr...", "thumbnail": "https://i.ytimg.com/vi/f-_6nHwJDp8/hqdefault.jpg", "url": "https://www.youtube.com/watch?v=f-_6nHwJDp8", "duration": "10:50", "view_count": 8448, "like_count": 265, "published_date": "2021-09-28", "channel_name": "Monsterlessons Academy"}]}}