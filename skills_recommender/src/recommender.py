import json
import psycopg2 as pg
import os
from typing import List, Dict, Any
from settings import POSTGRES_URL


class Node:
    def __init__(self):
        self.factor = 0
        self.rep = 0

    def add(self, ft):
        self.rep += 1
        self.factor += ft

    def average(self):
        if self.rep == 0:
            return 0
        return self.factor // self.rep

    def __repr__(self):
        return str(self.average())


def get_db_connection():
    try:
        # Try environment variables first
        # db_url = os.getenv('DATABASE_URL')
        if POSTGRES_URL:
            return pg.connect(POSTGRES_URL)

        # Fallback to hardcoded connection
        postgres_url = POSTGRES_URL
        return pg.connect(postgres_url)
    except Exception as e:
        print(f"Database connection error: {e}")
        # Try alternative connection
        try:
            # postgres_url = "postgresql://localhost:5432/skill_sage?user=postgres&password=christme"
            return pg.connect(POSTGRES_URL)
        except Exception as e2:
            print(f"Alternative connection failed: {e2}")
            raise


def recommend(skills: List[str], take: int = 20) -> List[str]:
    """
    Recommend skills based on input skills using collaborative filtering
    """
    if not skills:
        return []

    try:
        conn = get_db_connection()
        conn.autocommit = True
        cur = conn.cursor()

        # Convert skills to lowercase for matching
        skills_lower = [skill.lower().strip()
                        for skill in skills if skill.strip()]

        if not skills_lower:
            return []

        # Get skill factors for input skills
        fq = """
        SELECT skill, factor FROM skill_factors WHERE skill = ANY(%s);
        """
        cur.execute(fq, (skills_lower,))
        factor_records = cur.fetchall()

        factors = {}
        for item in factor_records:
            if item and len(item) >= 2:
                factors[item[0]] = item[1]

        if not factors:
            print(f"No factors found for skills: {skills_lower}")
            return []

        # Calculate skill recommendations
        pairs = {}
        for skill in skills_lower:
            if skill not in factors:
                continue

            skill_factors = factors[skill]
            if isinstance(skill_factors, dict):
                for k, v in skill_factors.items():
                    if k not in pairs:
                        pairs[k] = Node()
                    pairs[k].add(v)

        # Sort by average factor
        pair_list = []
        for k, v in pairs.items():
            avg = v.average()
            if avg > 0:  # Only include skills with positive factors
                pair_list.append({"skill": k, "average": avg})

        # Sort by average factor (descending)
        result = [
            x["skill"] for x in
            sorted(pair_list, key=lambda x: x["average"], reverse=True)
        ]

        # Filter out input skills and limit results
        clean = [skill for skill in result if skill not in skills_lower][:take]

        if not clean:
            return []

        # Get proper skill names
        pq = """
        SELECT name FROM skills WHERE lower = ANY(%s);
        """
        cur.execute(pq, (clean,))
        pair_records = cur.fetchall()

        recommended_skills = [record[0] for record in pair_records if record]

        conn.close()
        return recommended_skills

    except Exception as e:
        print(f"Error in recommend function: {e}")
        return []


print(recommend(["javascript", "html", "jquery", "csss"]))
