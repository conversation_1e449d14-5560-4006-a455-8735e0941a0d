import requests
import json
import PyPDF2
from settings import GEMINI_API_KEY


def pdf_to_text(pdf_path):
    """Read all text from a PDF."""
    text = ""
    with open(pdf_path, "rb") as f:
        reader = PyPDF2.PdfReader(f)
        for page in reader.pages:
            page_text = page.extract_text()
            if page_text:
                text += page_text + "\n"
    return text.strip()


def parse_skills_list(skills_text):
    """
    Convert Gemini's bullet-style skills output into a clean Python list of strings.
    """
    skills = []
    for line in skills_text.splitlines():
        line = line.strip()
        # Remove bullets and extra spaces
        if line.startswith("*"):
            line = line.lstrip("*").strip()
        if line:  # Skip empty lines
            skills.append(line)
    return skills


def extract_skills_from_pdf(pdf_path):
    """Send PDF text to Gemini API and get only the skills as a list."""
    # Step 1 — Read PDF into text
    pdf_text = pdf_to_text(pdf_path)

    # Step 2 — Append instruction for Gemini
    prompt_text = f"{pdf_text}\n\nExtract only the skills from this text as a list."

    # Step 3 — Gemini API request
    url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent"
    payload = json.dumps({
        "contents": [
            {
                "parts": [
                    {"text": prompt_text}
                ]
            }
        ]
    })
    headers = {
        'Content-Type': 'application/json',
        'X-goog-api-key': GEMINI_API_KEY
    }

    response = requests.post(url, headers=headers, data=payload)

    # Step 4 — Parse output
    resp_dict = response.json()
    skills_text = resp_dict['candidates'][0]['content']['parts'][0]['text'].strip(
    )

    return skills_text


# Example usage:
if __name__ == "__main__":
    skills_list_text = extract_skills_from_pdf("Emmanuel Nartey CV.pdf")
    skills_list = parse_skills_list(skills_list_text)
    print(skills_list)
