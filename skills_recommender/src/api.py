#!/usr/bin/env python3
"""
Skills Recommender API Server
Provides skill recommendations based on collaborative filtering
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import sys
from recommender import recommend

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes


@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "service": "skills-recommender",
        "version": "1.0.0"
    })


@app.route('/recommend/', methods=['POST'])
def recommend_skills():
    """
    Recommend skills based on input skills

    Request body:
    {
        "skills": ["python", "javascript", "react"],
        "limit": 10
    }

    Response:
    {
        "success": true,
        "recommendations": ["node.js", "express", "mongodb", ...],
        "count": 10
    }
    """
    skills = ['python', 'javascript', 'react']
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        skills = data.get('skills', [])
        limit = data.get('limit', 20)

        if not skills:
            return jsonify({
                "success": False,
                "error": "No skills provided"
            }), 400

        if not isinstance(skills, list):
            return jsonify({
                "success": False,
                "error": "Skills must be a list"
            }), 400

        # Validate limit
        if not isinstance(limit, int) or limit < 1 or limit > 100:
            limit = 20

        # Get recommendations

        recommendations = recommend(skills, take=limit)

        return jsonify({
            "success": True,
            "recommendations": recommendations,
            "count": len(recommendations),
            "input_skills": skills
        })

    except Exception as e:
        print(f"Error in recommend_skills: {e}")
        return jsonify({
            "success": False,
            "error": f"Internal server error: {str(e)}"
        }), 500


@app.route('/recommend/<skill>', methods=['GET'])
def recommend_single_skill(skill):
    """
    Recommend skills for a single skill (GET endpoint)

    URL: /recommend/python?limit=10

    Response:
    {
        "success": true,
        "recommendations": ["django", "flask", "pandas", ...],
        "count": 10
    }
    """
    try:
        limit = request.args.get('limit', 20, type=int)

        # Validate limit
        if limit < 1 or limit > 100:
            limit = 20

        # Get recommendations
        recommendations = recommend([skill], take=limit)

        return jsonify({
            "success": True,
            "recommendations": recommendations,
            "count": len(recommendations),
            "input_skill": skill
        })

    except Exception as e:
        print(f"Error in recommend_single_skill: {e}")
        return jsonify({
            "success": False,
            "error": f"Internal server error: {str(e)}"
        }), 500


@app.errorhandler(404)
def not_found(error):
    return jsonify({
        "success": False,
        "error": "Endpoint not found"
    }), 404


@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        "success": False,
        "error": "Internal server error"
    }), 500


if __name__ == '__main__':
    # Get port from environment variable or default to 5000
    port = int(os.environ.get('PORT', 8005))

    # Get host from environment variable or default to localhost
    host = os.environ.get('HOST', '0.0.0.0')

    # Get debug mode from environment variable
    debug = os.environ.get('DEBUG', 'True').lower() == 'true'

    print(f"Starting Skills Recommender API on {host}:{port}")
    print(f"Debug mode: {debug}")

    app.run(host=host, port=port, debug=debug)
