#!/usr/bin/env python3
"""
Skills Recommender Service Startup Script
"""

import os
import sys
import subprocess

def setup_environment():
    """Setup the environment and install dependencies"""
    print("Setting up Skills Recommender environment...")
    
    # Change to the src directory
    src_dir = os.path.join(os.path.dirname(__file__), 'src')
    os.chdir(src_dir)
    
    # Install requirements if needed
    try:
        import flask
        import psycopg2
        import pandas
        print("✅ Dependencies already installed")
    except ImportError:
        print("📦 Installing dependencies...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', '../requirements.txt'])
    
    return src_dir

def test_database_connection():
    """Test database connection"""
    print("🔍 Testing database connection...")
    try:
        from recommender import get_db_connection
        conn = get_db_connection()
        cur = conn.cursor()
        cur.execute("SELECT 1")
        result = cur.fetchone()
        conn.close()
        print("✅ Database connection successful")
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def test_recommender():
    """Test the recommender function"""
    print("🧪 Testing recommender function...")
    try:
        from recommender import recommend
        test_skills = ["python", "javascript"]
        recommendations = recommend(test_skills, take=5)
        print(f"✅ Recommender test successful: {len(recommendations)} recommendations")
        if recommendations:
            print(f"   Sample recommendations: {recommendations[:3]}")
        return True
    except Exception as e:
        print(f"❌ Recommender test failed: {e}")
        return False

def start_api_server():
    """Start the API server"""
    print("🚀 Starting Skills Recommender API server...")
    try:
        from api import app
        
        # Set environment variables
        os.environ.setdefault('HOST', '0.0.0.0')
        os.environ.setdefault('PORT', '5000')
        os.environ.setdefault('DEBUG', 'True')
        
        host = os.environ.get('HOST', '0.0.0.0')
        port = int(os.environ.get('PORT', 5000))
        debug = os.environ.get('DEBUG', 'False').lower() == 'true'
        
        print(f"🌐 Server starting on http://{host}:{port}")
        print("📋 Available endpoints:")
        print(f"   GET  http://{host}:{port}/health")
        print(f"   POST http://{host}:{port}/recommend")
        print(f"   GET  http://{host}:{port}/recommend/<skill>")
        print("\n🔄 Press Ctrl+C to stop the server")
        
        app.run(host=host, port=port, debug=debug)
        
    except Exception as e:
        print(f"❌ Failed to start API server: {e}")
        return False

def main():
    """Main startup function"""
    print("=" * 60)
    print("🎯 Skills Recommender Service")
    print("=" * 60)
    
    # Setup environment
    src_dir = setup_environment()
    
    # Test database connection
    if not test_database_connection():
        print("\n❌ Cannot start service without database connection")
        print("💡 Please check your database configuration and try again")
        return 1
    
    # Test recommender
    if not test_recommender():
        print("\n⚠️  Recommender test failed, but starting server anyway")
    
    # Start API server
    try:
        start_api_server()
    except KeyboardInterrupt:
        print("\n\n👋 Skills Recommender service stopped")
        return 0
    except Exception as e:
        print(f"\n❌ Service failed: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
