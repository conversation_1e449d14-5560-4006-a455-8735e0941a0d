{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python Debugger: Current File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal"
        },
        {
            "name": "Skill-Sage",
            "cwd": "Skill-Sage",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "<PERSON><PERSON>-<PERSON> (profile mode)",
            "cwd": "Skill-<PERSON>",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "Skill-<PERSON> (release mode)",
            "cwd": "Skill-Sage",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "a11y_assessments",
            "cwd": "Skill-Sage/flutter/dev/a11y_assessments",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "a11y_assessments (profile mode)",
            "cwd": "Skill-Sage/flutter/dev/a11y_assessments",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "a11y_assessments (release mode)",
            "cwd": "Skill-Sage/flutter/dev/a11y_assessments",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "automated_tests",
            "cwd": "Skill-Sage/flutter/dev/automated_tests",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "automated_tests (profile mode)",
            "cwd": "Skill-Sage/flutter/dev/automated_tests",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "automated_tests (release mode)",
            "cwd": "Skill-Sage/flutter/dev/automated_tests",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "bots",
            "cwd": "Skill-Sage/flutter/dev/bots",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "customer_testing",
            "cwd": "Skill-Sage/flutter/dev/customer_testing",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "devicelab",
            "cwd": "Skill-Sage/flutter/dev/devicelab",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "forbidden_from_release_tests",
            "cwd": "Skill-Sage/flutter/dev/forbidden_from_release_tests",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "manual_tests",
            "cwd": "Skill-Sage/flutter/dev/manual_tests",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "manual_tests (profile mode)",
            "cwd": "Skill-Sage/flutter/dev/manual_tests",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "manual_tests (release mode)",
            "cwd": "Skill-Sage/flutter/dev/manual_tests",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "missing_dependency_tests",
            "cwd": "Skill-Sage/flutter/dev/missing_dependency_tests",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "missing_dependency_tests (profile mode)",
            "cwd": "Skill-Sage/flutter/dev/missing_dependency_tests",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "missing_dependency_tests (release mode)",
            "cwd": "Skill-Sage/flutter/dev/missing_dependency_tests",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "snippets",
            "cwd": "Skill-Sage/flutter/dev/snippets",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "tools",
            "cwd": "Skill-Sage/flutter/dev/tools",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "tracing_tests",
            "cwd": "Skill-Sage/flutter/dev/tracing_tests",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "tracing_tests (profile mode)",
            "cwd": "Skill-Sage/flutter/dev/tracing_tests",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "tracing_tests (release mode)",
            "cwd": "Skill-Sage/flutter/dev/tracing_tests",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "api",
            "cwd": "Skill-Sage/flutter/examples/api",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "api (profile mode)",
            "cwd": "Skill-Sage/flutter/examples/api",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "api (release mode)",
            "cwd": "Skill-Sage/flutter/examples/api",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "flutter_view",
            "cwd": "Skill-Sage/flutter/examples/flutter_view",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "flutter_view (profile mode)",
            "cwd": "Skill-Sage/flutter/examples/flutter_view",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "flutter_view (release mode)",
            "cwd": "Skill-Sage/flutter/examples/flutter_view",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "hello_world",
            "cwd": "Skill-Sage/flutter/examples/hello_world",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "hello_world (profile mode)",
            "cwd": "Skill-Sage/flutter/examples/hello_world",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "hello_world (release mode)",
            "cwd": "Skill-Sage/flutter/examples/hello_world",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "image_list",
            "cwd": "Skill-Sage/flutter/examples/image_list",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "image_list (profile mode)",
            "cwd": "Skill-Sage/flutter/examples/image_list",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "image_list (release mode)",
            "cwd": "Skill-Sage/flutter/examples/image_list",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "layers",
            "cwd": "Skill-Sage/flutter/examples/layers",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "layers (profile mode)",
            "cwd": "Skill-Sage/flutter/examples/layers",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "layers (release mode)",
            "cwd": "Skill-Sage/flutter/examples/layers",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "platform_channel",
            "cwd": "Skill-Sage/flutter/examples/platform_channel",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "platform_channel (profile mode)",
            "cwd": "Skill-Sage/flutter/examples/platform_channel",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "platform_channel (release mode)",
            "cwd": "Skill-Sage/flutter/examples/platform_channel",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "platform_channel_swift",
            "cwd": "Skill-Sage/flutter/examples/platform_channel_swift",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "platform_channel_swift (profile mode)",
            "cwd": "Skill-Sage/flutter/examples/platform_channel_swift",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "platform_channel_swift (release mode)",
            "cwd": "Skill-Sage/flutter/examples/platform_channel_swift",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "platform_view",
            "cwd": "Skill-Sage/flutter/examples/platform_view",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "platform_view (profile mode)",
            "cwd": "Skill-Sage/flutter/examples/platform_view",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "platform_view (release mode)",
            "cwd": "Skill-Sage/flutter/examples/platform_view",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "splash",
            "cwd": "Skill-Sage/flutter/examples/splash",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "splash (profile mode)",
            "cwd": "Skill-Sage/flutter/examples/splash",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "splash (release mode)",
            "cwd": "Skill-Sage/flutter/examples/splash",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "texture",
            "cwd": "Skill-Sage/flutter/examples/texture",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "texture (profile mode)",
            "cwd": "Skill-Sage/flutter/examples/texture",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "texture (release mode)",
            "cwd": "Skill-Sage/flutter/examples/texture",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "flutter",
            "cwd": "Skill-Sage/flutter/packages/flutter",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "flutter (profile mode)",
            "cwd": "Skill-Sage/flutter/packages/flutter",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "flutter (release mode)",
            "cwd": "Skill-Sage/flutter/packages/flutter",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "flutter_driver",
            "cwd": "Skill-Sage/flutter/packages/flutter_driver",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "flutter_driver (profile mode)",
            "cwd": "Skill-Sage/flutter/packages/flutter_driver",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "flutter_driver (release mode)",
            "cwd": "Skill-Sage/flutter/packages/flutter_driver",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "flutter_goldens",
            "cwd": "Skill-Sage/flutter/packages/flutter_goldens",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "flutter_goldens (profile mode)",
            "cwd": "Skill-Sage/flutter/packages/flutter_goldens",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "flutter_goldens (release mode)",
            "cwd": "Skill-Sage/flutter/packages/flutter_goldens",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "flutter_localizations",
            "cwd": "Skill-Sage/flutter/packages/flutter_localizations",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "flutter_localizations (profile mode)",
            "cwd": "Skill-Sage/flutter/packages/flutter_localizations",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "flutter_localizations (release mode)",
            "cwd": "Skill-Sage/flutter/packages/flutter_localizations",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "flutter_test",
            "cwd": "Skill-Sage/flutter/packages/flutter_test",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "flutter_test (profile mode)",
            "cwd": "Skill-Sage/flutter/packages/flutter_test",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "flutter_test (release mode)",
            "cwd": "Skill-Sage/flutter/packages/flutter_test",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "flutter_tools",
            "cwd": "Skill-Sage/flutter/packages/flutter_tools",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "flutter_web_plugins",
            "cwd": "Skill-Sage/flutter/packages/flutter_web_plugins",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "flutter_web_plugins (profile mode)",
            "cwd": "Skill-Sage/flutter/packages/flutter_web_plugins",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "flutter_web_plugins (release mode)",
            "cwd": "Skill-Sage/flutter/packages/flutter_web_plugins",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "fuchsia_remote_debug_protocol",
            "cwd": "Skill-Sage/flutter/packages/fuchsia_remote_debug_protocol",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "integration_test",
            "cwd": "Skill-Sage/flutter/packages/integration_test",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "integration_test (profile mode)",
            "cwd": "Skill-Sage/flutter/packages/integration_test",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "integration_test (release mode)",
            "cwd": "Skill-Sage/flutter/packages/integration_test",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        }
    ]
}